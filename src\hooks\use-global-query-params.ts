import {
  parseAsInteger,
  parseAsString,
  parseAsArrayOf,
  useQueryStates,
} from "nuqs";
import { useMemo } from "react";

export function useGlobalQueryParams() {
  const [searchParams, setSearchParams] = useQueryStates(
    {
      limit: parseAsInteger.withDefault(12),
      next: parseAsInteger.withDefault(1),
      maxPrice: parseAsInteger,
      minPrice: parseAsInteger,
      order: parseAsString,
      evaluation: parseAsString,
      extl_id: parseAsString,
      categories: parseAsArrayOf(parseAsString),
      locations: parseAsArrayOf(parseAsString),
      currency: parseAsString.withDefault("EUR"),
      display: parseAsString,
    },
    {
      history: "push",
      shallow: false,
    },
  );

  const queryLength = useMemo(() => {
    const { categories, locations, minPrice, maxPrice, order } = searchParams;
    let count = 0;

    if (categories?.length) count++;
    if (locations?.length) count++;
    if (minPrice != null) count++;
    if (maxPrice != null) count++;
    if (order) count++;

    return count;
  }, [searchParams]);

  return {
    searchParams,
    setSearchParams,
    queryLength,
  };
}
